/* CSS Variables for Theme Support */
:root {
  /* Dark theme (default) */
  --bg-primary: #111;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #333;
  --text-primary: #fff;
  --text-secondary: #aaa;
  --text-tertiary: #888;
  --text-quaternary: #ccc;
  --border-primary: #333;
  --border-secondary: #555;
  --border-tertiary: #444;
  --accent-green: #00ff00;
  --accent-green-hover: #00cc00;
  --accent-orange: orange;
  --accent-red: #ff4444;
  --accent-yellow: #ffaa44;
  --shadow-primary: rgba(0,0,0,0.5);
  --shadow-green: rgba(0, 255, 0, 0.2);
  --shadow-green-strong: rgba(0, 255, 0, 0.3);
  --tooltip-bg: #2a2a2a;
  --volume-bg: #333;
}

/* Light theme */
body[data-theme="light"] {
  --bg-primary: #f5f5f5;
  --bg-secondary: #ffffff;
  --bg-tertiary: #e0e0e0;
  --text-primary: #333;
  --text-secondary: #666;
  --text-tertiary: #999;
  --text-quaternary: #444;
  --border-primary: #ddd;
  --border-secondary: #ccc;
  --border-tertiary: #bbb;
  --accent-green: #00aa00;
  --accent-green-hover: #008800;
  --accent-orange: #ff8c00;
  --accent-red: #cc3333;
  --accent-yellow: #cc8800;
  --shadow-primary: rgba(0,0,0,0.1);
  --shadow-green: rgba(0, 170, 0, 0.2);
  --shadow-green-strong: rgba(0, 170, 0, 0.3);
  --tooltip-bg: #f9f9f9;
  --volume-bg: #e0e0e0;
}

body {
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: Arial;
  padding: 20px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

h1 {
  margin: 0;
}

.subtitle {
  margin-bottom: 20px;
  color: var(--text-secondary);
}

/* Theme Toggle Button */
.theme-toggle {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 40px;
  height: 40px;
}

.theme-toggle:hover {
  background: var(--bg-tertiary);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-primary);
}

.theme-icon {
  color: var(--text-primary);
  transition: all 0.3s ease;
  position: absolute;
}

/* Show sun icon in dark theme, moon icon in light theme */
body[data-theme="dark"] .sun-icon {
  opacity: 1;
  transform: rotate(0deg);
}

body[data-theme="dark"] .moon-icon {
  opacity: 0;
  transform: rotate(180deg);
}

body[data-theme="light"] .sun-icon {
  opacity: 0;
  transform: rotate(-180deg);
}

body[data-theme="light"] .moon-icon {
  opacity: 1;
  transform: rotate(0deg);
}
    .coins-container {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }
    .coin-card {
      background: var(--bg-secondary);
      border: 1px solid var(--border-primary);
      border-radius: 8px;
      padding: 12px 16px;
      display: flex;
      flex-direction: column;
      gap: 6px;
      transition: all 0.3s ease;
      min-width: 200px;
    }
    .coin-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px var(--shadow-green);
    }
    .coin-card .pair-name {
      color: var(--text-primary);
      font-weight: bold;
      font-size: 1.1em;
    }
    .change { font-weight: bold; }
    .green { color: var(--accent-green); }
    .spike {
      color: var(--accent-orange);
      font-size: 0.85em;
      font-weight: bold;
      background: rgba(255, 165, 0, 0.1);
      padding: 2px 6px;
      border-radius: 4px;
      border: 1px solid rgba(255, 165, 0, 0.3);
    }
    .rsi {
      font-size: 0.9em;
      font-weight: bold;
      padding: 2px 6px;
      border-radius: 4px;
      border: 1px solid;
    }
    .rsi.oversold {
      color: var(--accent-red);
      background: rgba(255, 68, 68, 0.1);
      border-color: rgba(255, 68, 68, 0.3);
    }
    .rsi.overbought {
      color: var(--accent-green);
      background: rgba(68, 255, 68, 0.1);
      border-color: rgba(68, 255, 68, 0.3);
    }
    .rsi.neutral {
      color: var(--accent-yellow);
      background: rgba(255, 170, 68, 0.1);
      border-color: rgba(255, 170, 68, 0.3);
    }
    .price {
      color: var(--text-secondary);
      font-size: 0.85em;
    }
    .volume-container {
      margin-top: 6px;
    }
    .volume-label {
      font-size: 0.75em;
      color: var(--text-tertiary);
      margin-bottom: 2px;
    }
    .volume-bar {
      height: 6px;
      background: var(--volume-bg);
      border-radius: 3px;
      overflow: hidden;
      position: relative;
    }
    .volume-fill {
      height: 100%;
      border-radius: 3px;
      transition: width 0.5s ease;
      position: relative;
    }
    .volume-fill.normal {
      background: linear-gradient(90deg, #4a9eff, #00ff88);
    }
    .volume-fill.spike {
      background: linear-gradient(90deg, #ff6b35, #f7931e);
      animation: pulse 1.5s infinite;
    }
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }
    .volume-text {
      font-size: 0.7em;
      color: var(--text-secondary);
      margin-top: 2px;
    }

    /* Configuration Panel Styles */
    .config-panel {
      background: var(--bg-secondary);
      border: 1px solid var(--border-primary);
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      align-items: end;
      transition: all 0.3s ease;
    }
    .config-group {
      display: flex;
      flex-direction: column;
      gap: 5px;
    }
    .config-group label {
      font-size: 0.9em;
      color: var(--text-quaternary);
      font-weight: bold;
      display: flex;
      align-items: center;
      gap: 6px;
    }
    .config-group select,
    .config-group input {
      background: var(--bg-tertiary);
      border: 1px solid var(--border-secondary);
      border-radius: 4px;
      color: var(--text-primary);
      padding: 8px 12px;
      font-size: 0.9em;
      min-width: 100px;
      transition: all 0.3s ease;
    }
    .config-group select:focus,
    .config-group input:focus {
      outline: none;
      border-color: var(--accent-green);
    }
    .apply-btn {
      background: linear-gradient(135deg, var(--accent-green), var(--accent-green-hover));
      border: none;
      border-radius: 6px;
      color: var(--bg-primary);
      padding: 10px 20px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    .apply-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px var(--shadow-green-strong);
    }
    .apply-btn:active {
      transform: translateY(0);
    }
    .config-status {
      font-size: 0.85em;
      color: var(--text-tertiary);
      margin-left: auto;
      align-self: center;
    }

    /* Info Icon and Tooltip Styles */
    .info-icon {
      cursor: help;
      font-size: 0.8em;
      color: var(--text-tertiary);
      transition: color 0.3s ease;
      position: relative;
    }
    .info-icon svg{
        width: 10px;
    }
    .info-icon:hover {
      color: var(--accent-green);
    }
    .info-icon::after {
        content: attr(data-tooltip);
        position: absolute;
        bottom: 120%;
        left: 150px;
        transform: translateX(-50%);
        background: var(--tooltip-bg);
        color: var(--text-primary);
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 0.95em;
        white-space: nowrap;
        max-width: 300px;
        white-space: normal;
        width: max-content;
        max-width: 280px;
        box-shadow: 0 4px 12px var(--shadow-primary);
        border: 1px solid var(--border-tertiary);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1000;
        font-weight: normal;
        line-height: 1.3;
    }
    .info-icon::before {
        content: '';
        position: absolute;
        bottom: 65%;
        left: 4px;
        transform: translateX(-50%);
        border: 5px solid transparent;
        border-top-color: var(--border-tertiary);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1001;
    }
    .info-icon:hover::after,
    .info-icon:hover::before {
      opacity: 1;
      visibility: visible;
    }